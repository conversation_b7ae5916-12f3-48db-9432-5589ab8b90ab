// package cn.wbw.yyhis.config;
//
// import cn.wbw.yyhis.filter.UrlDecodeFilter;
// import lombok.RequiredArgsConstructor;
// import org.springframework.boot.web.servlet.FilterRegistrationBean;
// import org.springframework.context.annotation.Bean;
// import org.springframework.context.annotation.Configuration;
//
// @Configuration
// @RequiredArgsConstructor
// public class FilterConfig {
//
//     @Bean
//     public FilterRegistrationBean<UrlDecodeFilter> urlDecodeFilterRegistration() {
//         FilterRegistrationBean<UrlDecodeFilter> registration = new FilterRegistrationBean<>();
//         registration.setFilter(new UrlDecodeFilter());
//         registration.addUrlPatterns("/*");
//         registration.setName("urlDecodeFilter");
//         registration.setOrder(1);
//         return registration;
//     }
// }
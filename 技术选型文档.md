# YYHIS-Server 技术选型文档

## 项目概述

**项目名称**: YYHIS-Server (医院信息系统服务端)  
**项目版本**: 0.0.1-SNAPSHOT  
**开发团队**: cn.wbw  
**项目类型**: 医疗信息系统后端服务  

## 技术架构总览

本项目采用现代化的Java企业级开发技术栈，基于Spring Boot 3.x构建，面向医疗行业的数据集成和管理需求。

## 核心技术选型

### 1. 基础框架层

#### 1.1 Java运行环境
- **Java版本**: Java 21 (LTS)
- **选型理由**: 
  - 最新的长期支持版本，提供更好的性能和安全性
  - 支持虚拟线程等现代化特性
  - 与Spring Boot 3.x完美兼容

#### 1.2 Spring生态系统
- **Spring Boot**: 3.5.3
- **核心模块**:
  - `spring-boot-starter-web`: Web应用开发
  - `spring-boot-starter-aop`: 面向切面编程支持
  - `spring-boot-starter-validation`: 参数校验
  - `spring-boot-devtools`: 开发工具支持
- **选型理由**:
  - 成熟稳定的企业级框架
  - 自动配置减少开发复杂度
  - 丰富的生态系统支持
  - 优秀的社区支持和文档

### 2. 数据持久化层

#### 2.1 数据库
- **数据库**: MySQL 8.x
- **驱动**: mysql-connector-j (最新版本)
- **连接配置**: 
  - 支持SSL关闭配置
  - 时区设置为GMT+8
  - 批量重写语句优化
- **选型理由**:
  - 医疗行业广泛使用，稳定可靠
  - 支持ACID事务，数据一致性强
  - 性能优秀，支持大数据量处理

#### 2.2 ORM框架
- **MyBatis Plus**: 3.5.12
- **核心特性**:
  - 自动分页插件
  - 代码生成器支持
  - 增强的CRUD操作
  - SQL解析器支持
- **选型理由**:
  - 基于MyBatis的增强工具
  - 减少样板代码，提高开发效率
  - 灵活的SQL控制能力
  - 适合复杂业务查询场景

### 3. Web服务层

#### 3.1 API文档
- **Springdoc OpenAPI**: 2.2.0
- **功能**: 自动生成Swagger UI文档
- **选型理由**:
  - 与Spring Boot 3.x原生集成
  - 自动生成API文档，减少维护成本
  - 支持在线测试功能

#### 3.2 数据序列化
- **Jackson**: Spring Boot内置
- **自定义配置**:
  - 时间格式统一处理
  - 蛇形命名法支持
  - 时区设置为Asia/Shanghai
- **选型理由**:
  - Spring Boot默认JSON处理器
  - 性能优秀，功能完善
  - 支持灵活的自定义配置

### 4. 开发效率工具

#### 4.1 代码简化
- **Lombok**: 最新版本
- **功能**: 自动生成getter/setter、构造函数等
- **选型理由**:
  - 减少样板代码
  - 提高代码可读性
  - 编译时处理，无运行时开销

#### 4.2 对象映射
- **MapStruct**: 1.5.5.Final
- **功能**: 编译时生成类型安全的映射代码
- **选型理由**:
  - 编译时生成，性能优秀
  - 类型安全，减少运行时错误
  - 支持复杂映射规则

#### 4.3 工具库
- **Hutool**: 5.8.22
- **功能**: 提供丰富的Java工具类
- **选型理由**:
  - 国产优秀工具库
  - 功能全面，API简洁
  - 适合中国开发者使用习惯

### 5. 办公文档处理

#### 5.1 Excel处理
- **Apache POI**: 5.2.5
- **模块**:
  - `poi`: 核心库
  - `poi-ooxml`: Office Open XML格式支持
- **选型理由**:
  - 医疗数据导入导出需求
  - 成熟稳定的Office文档处理库
  - 支持复杂的Excel操作

### 6. 构建和部署

#### 6.1 构建工具
- **Maven**: 3.x
- **Maven Wrapper**: 包含mvnw脚本
- **选型理由**:
  - 标准的Java项目构建工具
  - 依赖管理简单明确
  - 与IDE集成良好

#### 6.2 容器化部署
- **Docker**: 支持
- **基础镜像**: openjdk:21-jdk-slim
- **特性**:
  - 轻量级镜像
  - 时区配置
  - JVM参数优化
- **选型理由**:
  - 标准化部署环境
  - 便于CI/CD集成
  - 资源占用优化

### 7. 测试框架

#### 7.1 单元测试
- **JUnit 5**: Spring Boot内置
- **Spring Boot Test**: 集成测试支持
- **选型理由**:
  - 现代化的测试框架
  - 与Spring生态完美集成
  - 支持参数化测试等高级特性

## 架构设计模式

### 1. 分层架构
```
Controller层 -> Service层 -> Mapper层 -> Database
     |              |           |
   DTO/VO      Business Logic  Data Access
```

### 2. 核心设计模式
- **MVC模式**: 标准的Web应用架构
- **依赖注入**: Spring IoC容器管理
- **AOP切面**: 横切关注点处理
- **转换器模式**: MapStruct对象映射
- **统一响应**: ApiResult包装所有API响应

### 3. 异常处理策略
- **全局异常处理器**: 统一异常处理和响应格式
- **业务异常**: 自定义BusinessException
- **参数校验**: Bean Validation支持
- **日志记录**: SLF4J + Logback

## 配置管理

### 1. 多环境配置
- **开发环境**: application-dev.yml
- **生产环境**: application-prod.yml
- **公共配置**: application.yml

### 2. 外部集成配置
- **第三方API**: 统一的integration配置
- **超时设置**: 连接和读取超时配置
- **认证信息**: appKey和appSecret管理

## 性能优化策略

### 1. 数据库优化
- **连接池**: HikariCP (Spring Boot默认)
- **批量操作**: rewriteBatchedStatements=true
- **分页查询**: MyBatis Plus分页插件

### 2. 应用优化
- **异步处理**: @EnableAsync支持
- **JVM优化**: Docker容器JVM参数调优
- **对象映射**: MapStruct编译时生成，零反射开销

## 安全考虑

### 1. 数据安全
- **参数校验**: 多层次参数验证
- **SQL注入防护**: MyBatis预编译语句
- **异常信息**: 敏感信息脱敏处理

### 2. 运行时安全
- **时区统一**: 避免时间相关的安全问题
- **依赖管理**: 使用最新稳定版本
- **容器安全**: 最小化镜像，减少攻击面

## 可维护性设计

### 1. 代码组织
- **包结构清晰**: 按功能模块组织
- **命名规范**: 统一的命名约定
- **注释文档**: 关键业务逻辑注释

### 2. 扩展性支持
- **配置外部化**: 支持不同环境配置
- **接口设计**: RESTful API设计
- **模块化**: 清晰的模块边界

## 技术选型总结

本项目的技术选型充分考虑了医疗行业的特殊需求：

1. **稳定性优先**: 选择成熟稳定的技术栈
2. **性能保证**: 采用高性能的组件和优化策略
3. **开发效率**: 使用现代化工具提高开发效率
4. **可维护性**: 清晰的架构设计和代码组织
5. **扩展性**: 支持未来业务发展需求

整体技术栈现代化程度高，既保证了系统的稳定性和性能，又提供了良好的开发体验和维护性。

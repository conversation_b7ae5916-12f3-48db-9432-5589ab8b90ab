package cn.wbw.yyhis.service;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * API文档导出服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ApiDocumentService {

    private final ApplicationContext applicationContext;

    /**
     * 生成API文档Word文件
     */
    public byte[] generateApiDocument() throws IOException {
        XWPFDocument document = new XWPFDocument();
        
        // 添加标题
        addTitle(document);
        
        // 添加项目信息
        addProjectInfo(document);
        
        // 获取所有Controller并生成文档
        Map<String, Object> controllers = applicationContext.getBeansWithAnnotation(RestController.class);
        
        // 添加接口概览
        addApiOverview(document, controllers);
        
        // 添加详细接口文档
        addDetailedApiDocs(document, controllers);
        
        // 转换为字节数组
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        document.write(outputStream);
        document.close();
        
        return outputStream.toByteArray();
    }

    /**
     * 添加文档标题
     */
    private void addTitle(XWPFDocument document) {
        XWPFParagraph titleParagraph = document.createParagraph();
        titleParagraph.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun titleRun = titleParagraph.createRun();
        titleRun.setText("YYHIS-Server API接口文档");
        titleRun.setBold(true);
        titleRun.setFontSize(20);
        titleRun.setFontFamily("微软雅黑");
        
        // 添加空行
        document.createParagraph();
    }

    /**
     * 添加项目信息
     */
    private void addProjectInfo(XWPFDocument document) {
        XWPFParagraph infoParagraph = document.createParagraph();
        XWPFRun infoRun = infoParagraph.createRun();
        infoRun.setText("项目名称：YYHIS-Server (医院信息系统服务端)");
        infoRun.addBreak();
        infoRun.setText("版本：0.0.1-SNAPSHOT");
        infoRun.addBreak();
        infoRun.setText("生成时间：" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        infoRun.addBreak();
        infoRun.setText("技术栈：Spring Boot 3.5.3 + MyBatis Plus + MySQL");
        infoRun.setFontFamily("微软雅黑");
        
        // 添加空行
        document.createParagraph();
    }

    /**
     * 添加接口概览
     */
    private void addApiOverview(XWPFDocument document, Map<String, Object> controllers) {
        // 添加概览标题
        XWPFParagraph overviewTitle = document.createParagraph();
        XWPFRun overviewRun = overviewTitle.createRun();
        overviewRun.setText("接口概览");
        overviewRun.setBold(true);
        overviewRun.setFontSize(16);
        overviewRun.setFontFamily("微软雅黑");
        
        // 创建表格
        XWPFTable table = document.createTable();
        table.setWidth("100%");
        
        // 表头
        XWPFTableRow headerRow = table.getRow(0);
        headerRow.getCell(0).setText("模块名称");
        headerRow.addNewTableCell().setText("接口数量");
        headerRow.addNewTableCell().setText("基础路径");
        headerRow.addNewTableCell().setText("描述");
        
        // 设置表头样式
        for (XWPFTableCell cell : headerRow.getTableCells()) {
            cell.setColor("E6E6FA");
            XWPFParagraph p = cell.getParagraphs().get(0);
            XWPFRun r = p.getRuns().get(0);
            r.setBold(true);
            r.setFontFamily("微软雅黑");
        }
        
        // 填充数据
        for (Map.Entry<String, Object> entry : controllers.entrySet()) {
            Object controller = entry.getValue();
            Class<?> controllerClass = controller.getClass();
            
            // 获取Controller信息
            String moduleName = getControllerName(controllerClass);
            String basePath = getBasePath(controllerClass);
            String description = getControllerDescription(controllerClass);
            int apiCount = getApiCount(controllerClass);
            
            XWPFTableRow row = table.createRow();
            row.getCell(0).setText(moduleName);
            row.getCell(1).setText(String.valueOf(apiCount));
            row.getCell(2).setText(basePath);
            row.getCell(3).setText(description);
            
            // 设置字体
            for (XWPFTableCell cell : row.getTableCells()) {
                XWPFParagraph p = cell.getParagraphs().get(0);
                if (!p.getRuns().isEmpty()) {
                    p.getRuns().get(0).setFontFamily("微软雅黑");
                }
            }
        }
        
        // 添加空行
        document.createParagraph();
    }

    /**
     * 添加详细接口文档
     */
    private void addDetailedApiDocs(XWPFDocument document, Map<String, Object> controllers) {
        // 添加详细文档标题
        XWPFParagraph detailTitle = document.createParagraph();
        XWPFRun detailRun = detailTitle.createRun();
        detailRun.setText("详细接口文档");
        detailRun.setBold(true);
        detailRun.setFontSize(16);
        detailRun.setFontFamily("微软雅黑");
        
        for (Map.Entry<String, Object> entry : controllers.entrySet()) {
            Object controller = entry.getValue();
            Class<?> controllerClass = controller.getClass();
            
            addControllerDoc(document, controllerClass);
        }
    }

    /**
     * 添加单个Controller的文档
     */
    private void addControllerDoc(XWPFDocument document, Class<?> controllerClass) {
        String controllerName = getControllerName(controllerClass);
        String description = getControllerDescription(controllerClass);
        String basePath = getBasePath(controllerClass);
        
        // Controller标题
        XWPFParagraph controllerTitle = document.createParagraph();
        XWPFRun controllerRun = controllerTitle.createRun();
        controllerRun.setText(controllerName + " - " + description);
        controllerRun.setBold(true);
        controllerRun.setFontSize(14);
        controllerRun.setFontFamily("微软雅黑");
        
        // 基础路径
        XWPFParagraph basePathPara = document.createParagraph();
        XWPFRun basePathRun = basePathPara.createRun();
        basePathRun.setText("基础路径：" + basePath);
        basePathRun.setFontFamily("微软雅黑");
        
        // 获取所有API方法
        Method[] methods = controllerClass.getDeclaredMethods();
        for (Method method : methods) {
            if (isApiMethod(method)) {
                addMethodDoc(document, method, basePath);
            }
        }
        
        // 添加分隔线
        document.createParagraph().createRun().setText("─".repeat(50));
    }

    /**
     * 添加方法文档
     */
    private void addMethodDoc(XWPFDocument document, Method method, String basePath) {
        String methodName = getMethodName(method);
        String httpMethod = getHttpMethod(method);
        String path = getMethodPath(method);
        String fullPath = basePath + path;
        
        XWPFParagraph methodPara = document.createParagraph();
        XWPFRun methodRun = methodPara.createRun();
        methodRun.setText("• " + methodName);
        methodRun.setBold(true);
        methodRun.setFontFamily("微软雅黑");
        
        XWPFParagraph detailPara = document.createParagraph();
        XWPFRun detailRun = detailPara.createRun();
        detailRun.setText("  HTTP方法：" + httpMethod);
        detailRun.addBreak();
        detailRun.setText("  请求路径：" + fullPath);
        detailRun.addBreak();
        detailRun.setText("  返回类型：" + method.getReturnType().getSimpleName());
        detailRun.setFontFamily("微软雅黑");
        
        // 参数信息
        if (method.getParameterCount() > 0) {
            detailRun.addBreak();
            detailRun.setText("  参数：");
            for (int i = 0; i < method.getParameterCount(); i++) {
                Class<?> paramType = method.getParameterTypes()[i];
                detailRun.addBreak();
                detailRun.setText("    - " + paramType.getSimpleName());
            }
        }
        
        document.createParagraph(); // 空行
    }

    // 辅助方法
    private String getControllerName(Class<?> controllerClass) {
        Tag tag = controllerClass.getAnnotation(Tag.class);
        if (tag != null && !tag.name().isEmpty()) {
            return tag.name();
        }
        return controllerClass.getSimpleName().replace("Controller", "");
    }

    private String getControllerDescription(Class<?> controllerClass) {
        Tag tag = controllerClass.getAnnotation(Tag.class);
        if (tag != null && !tag.description().isEmpty()) {
            return tag.description();
        }
        return "暂无描述";
    }

    private String getBasePath(Class<?> controllerClass) {
        RequestMapping requestMapping = controllerClass.getAnnotation(RequestMapping.class);
        if (requestMapping != null && requestMapping.value().length > 0) {
            return requestMapping.value()[0];
        }
        return "";
    }

    private int getApiCount(Class<?> controllerClass) {
        Method[] methods = controllerClass.getDeclaredMethods();
        int count = 0;
        for (Method method : methods) {
            if (isApiMethod(method)) {
                count++;
            }
        }
        return count;
    }

    private boolean isApiMethod(Method method) {
        return method.isAnnotationPresent(GetMapping.class) ||
               method.isAnnotationPresent(PostMapping.class) ||
               method.isAnnotationPresent(PutMapping.class) ||
               method.isAnnotationPresent(DeleteMapping.class) ||
               method.isAnnotationPresent(RequestMapping.class);
    }

    private String getMethodName(Method method) {
        Operation operation = method.getAnnotation(Operation.class);
        if (operation != null && !operation.summary().isEmpty()) {
            return operation.summary();
        }
        return method.getName();
    }

    private String getHttpMethod(Method method) {
        if (method.isAnnotationPresent(GetMapping.class)) return "GET";
        if (method.isAnnotationPresent(PostMapping.class)) return "POST";
        if (method.isAnnotationPresent(PutMapping.class)) return "PUT";
        if (method.isAnnotationPresent(DeleteMapping.class)) return "DELETE";
        if (method.isAnnotationPresent(RequestMapping.class)) {
            RequestMapping rm = method.getAnnotation(RequestMapping.class);
            if (rm.method().length > 0) {
                return rm.method()[0].name();
            }
        }
        return "GET";
    }

    private String getMethodPath(Method method) {
        if (method.isAnnotationPresent(GetMapping.class)) {
            GetMapping mapping = method.getAnnotation(GetMapping.class);
            return mapping.value().length > 0 ? mapping.value()[0] : "";
        }
        if (method.isAnnotationPresent(PostMapping.class)) {
            PostMapping mapping = method.getAnnotation(PostMapping.class);
            return mapping.value().length > 0 ? mapping.value()[0] : "";
        }
        if (method.isAnnotationPresent(PutMapping.class)) {
            PutMapping mapping = method.getAnnotation(PutMapping.class);
            return mapping.value().length > 0 ? mapping.value()[0] : "";
        }
        if (method.isAnnotationPresent(DeleteMapping.class)) {
            DeleteMapping mapping = method.getAnnotation(DeleteMapping.class);
            return mapping.value().length > 0 ? mapping.value()[0] : "";
        }
        if (method.isAnnotationPresent(RequestMapping.class)) {
            RequestMapping mapping = method.getAnnotation(RequestMapping.class);
            return mapping.value().length > 0 ? mapping.value()[0] : "";
        }
        return "";
    }
}

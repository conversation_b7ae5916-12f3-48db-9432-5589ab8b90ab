# API文档导出功能使用说明

## 功能概述

本功能为YYHIS-Server项目提供了将API接口自动导出为Word文档的能力，支持：

- 自动扫描所有Controller类
- 提取接口信息（URL、HTTP方法、参数、返回类型等）
- 生成格式化的Word文档
- 提供API统计信息

## 使用方法

### 1. 启动项目

确保项目正常启动，默认端口为6596：

```bash
mvn spring-boot:run
```

### 2. 导出API文档

#### 方法一：通过浏览器访问

访问以下URL直接下载Word文档：

```
http://localhost:6596/document/export/api
```

可选参数：
- `filename`: 自定义文件名前缀（默认为"YYHIS-API文档"）

示例：
```
http://localhost:6596/document/export/api?filename=医院系统接口文档
```

#### 方法二：通过Swagger UI

1. 访问Swagger UI：`http://localhost:6596/swagger-ui.html`
2. 找到"文档导出"模块
3. 展开"导出API接口文档"接口
4. 点击"Try it out"
5. 输入文件名（可选）
6. 点击"Execute"
7. 在响应中点击"Download file"

### 3. 查看API统计信息

获取当前系统API统计信息：

```
GET http://localhost:6596/document/api/stats
```

返回示例：
```json
{
  "totalControllers": 15,
  "totalApis": 48,
  "generatedTime": "2024-12-19 14:30:25"
}
```

## 生成的文档内容

导出的Word文档包含以下内容：

### 1. 文档标题和项目信息
- 项目名称：YYHIS-Server (医院信息系统服务端)
- 版本信息
- 生成时间
- 技术栈信息

### 2. 接口概览表
包含所有Controller的汇总信息：
- 模块名称
- 接口数量
- 基础路径
- 模块描述

### 3. 详细接口文档
按Controller分组，每个接口包含：
- 接口名称（来自@Operation注解）
- HTTP方法（GET/POST/PUT/DELETE）
- 完整请求路径
- 返回类型
- 参数信息

## 文档格式说明

- 使用微软雅黑字体，确保中文显示效果
- 标题使用不同字号区分层级
- 表格使用淡紫色表头
- 自动添加时间戳到文件名

## 技术实现

### 核心组件

1. **ApiDocumentService**: 负责文档生成逻辑
   - 扫描Spring容器中的Controller
   - 提取接口注解信息
   - 使用Apache POI生成Word文档

2. **DocumentExportController**: 提供HTTP接口
   - 文档导出接口
   - API统计接口

### 依赖技术

- **Apache POI**: Word文档生成
- **Spring Boot**: 框架支持
- **SpringDoc OpenAPI**: 接口注解支持
- **反射机制**: 动态获取接口信息

## 扩展功能

### 可以进一步扩展的功能：

1. **支持更多导出格式**
   - PDF格式
   - HTML格式
   - Markdown格式

2. **增强文档内容**
   - 请求/响应示例
   - 参数详细说明
   - 错误码说明

3. **自定义模板**
   - 支持自定义Word模板
   - 公司Logo和样式

4. **定时生成**
   - 定时任务自动生成
   - 版本对比功能

## 注意事项

1. **性能考虑**
   - 大型项目可能生成时间较长
   - 建议在开发环境使用

2. **权限控制**
   - 生产环境建议添加访问权限控制
   - 可以集成到现有的认证体系

3. **文档维护**
   - 确保Controller类添加了适当的注解
   - 定期更新文档内容

## 故障排除

### 常见问题

1. **文档生成失败**
   - 检查Apache POI依赖是否正确
   - 查看日志中的错误信息

2. **接口信息不完整**
   - 确保Controller使用了@RestController注解
   - 检查方法是否使用了正确的映射注解

3. **中文显示异常**
   - 确保系统支持UTF-8编码
   - 检查字体设置

### 日志查看

查看应用日志获取详细错误信息：
```bash
tail -f logs/application.log
```

## 联系支持

如有问题或建议，请联系开发团队。

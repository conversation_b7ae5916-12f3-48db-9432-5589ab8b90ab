package cn.wbw.yyhis.controller;

import cn.wbw.yyhis.service.ApiDocumentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 文档导出控制器
 */
@RestController
@RequestMapping("/document")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "文档导出", description = "API文档导出相关接口")
public class DocumentExportController {

    private final ApiDocumentService apiDocumentService;

    /**
     * 导出API接口文档为Word格式
     */
    @Operation(summary = "导出API接口文档", description = "将所有API接口信息导出为Word文档")
    @GetMapping("/export/api")
    public ResponseEntity<byte[]> exportApiDocument(
            @RequestParam(defaultValue = "YYHIS-API文档") String filename) {
        
        try {
            log.info("开始生成API文档...");
            
            // 生成文档
            byte[] documentBytes = apiDocumentService.generateApiDocument();
            
            // 生成文件名
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String finalFilename = filename + "_" + timestamp + ".docx";
            
            log.info("API文档生成完成，文件大小: {} bytes", documentBytes.length);
            
            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", finalFilename);
            headers.setContentLength(documentBytes.length);
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(documentBytes);
                    
        } catch (IOException e) {
            log.error("生成API文档失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取API统计信息
     */
    @Operation(summary = "获取API统计信息", description = "获取当前系统中API接口的统计信息")
    @GetMapping("/api/stats")
    public ResponseEntity<ApiDocumentService.ApiStats> getApiStats() {
        try {
            ApiDocumentService.ApiStats stats = apiDocumentService.getApiStatistics();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("获取API统计信息失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
